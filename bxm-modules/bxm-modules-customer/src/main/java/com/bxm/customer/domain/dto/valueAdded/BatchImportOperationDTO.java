package com.bxm.customer.domain.dto.valueAdded;

import com.bxm.customer.domain.enums.ValueAddedBatchImportOperationType;
import com.bxm.file.api.domain.RemoteAliFileDTO;
import com.bxm.common.core.utils.uuid.IdUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 批量导入操作DTO
 *
 * 用于增值交付单批量导入操作的参数封装
 * 支持交付、补充交付附件、扣款三种操作类型
 * 包含已上传文件的信息，用于异步处理
 *
 * <AUTHOR>
 * @date 2025-08-27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("批量导入操作DTO")
public class BatchImportOperationDTO {

    /**
     * 操作类型：DELIVERY-交付、SUPPLEMENT_DELIVERY-补充交付附件、DEDUCTION-扣款
     */
    @NotNull(message = "操作类型不能为空")
    @ApiModelProperty(value = "操作类型", required = true,
                     allowableValues = "DELIVERY,SUPPLEMENT_DELIVERY,DEDUCTION",
                     example = "DELIVERY")
    private ValueAddedBatchImportOperationType operation;

    /**
     * 交付单模板文件信息（已上传到OSS的文件信息）
     */
    @NotNull(message = "交付单模板文件信息不能为空")
    @ApiModelProperty(value = "交付单模板文件信息", required = true)
    private RemoteAliFileDTO templateFileInfo;

    /**
     * 附件压缩文件信息（已上传到OSS的文件信息，可选）
     */
    @ApiModelProperty(value = "附件压缩文件信息")
    private RemoteAliFileDTO attachmentFileInfo;

    /**
     * 附件解压后的临时目录路径（用于存放解压后的文件）
     */
    @ApiModelProperty(value = "附件解压后的临时目录路径")
    private String attachmentFileDir;

    /**
     * 获取操作描述
     */
    public String getOperationDescription() {
        if (operation == null) {
            return "未知操作";
        }
        return operation.getDescription();
    }

    /**
     * 判断是否有附件文件
     */
    public boolean hasAttachmentFile() {
        return attachmentFileInfo != null &&
               attachmentFileInfo.getUrl() != null &&
               !attachmentFileInfo.getUrl().trim().isEmpty();
    }

    /**
     * 判断是否已生成附件解压临时目录
     */
    public boolean hasAttachmentFileDir() {
        return attachmentFileDir != null && !attachmentFileDir.trim().isEmpty();
    }

    /**
     * 生成附件解压临时目录路径
     * 使用唯一ID确保目录名不冲突
     */
    public String generateAttachmentFileDir() {
        if (attachmentFileDir == null || attachmentFileDir.trim().isEmpty()) {
            // 使用UUID生成唯一目录名
            String uniqueId = IdUtils.fastSimpleUUID();
            attachmentFileDir = System.getProperty("java.io.tmpdir") + "/attachment_extract_" + uniqueId;
        }
        return attachmentFileDir;
    }

    /**
     * 验证必要参数
     */
    public void validate() {
        if (operation == null) {
            throw new IllegalArgumentException("操作类型不能为空");
        }

        if (templateFileInfo == null) {
            throw new IllegalArgumentException("模板文件信息不能为空");
        }

        // 验证模板文件格式
        String templateFileName = templateFileInfo.getFileName();
        if (templateFileName == null ||
            (!templateFileName.toLowerCase().endsWith(".xlsx") &&
             !templateFileName.toLowerCase().endsWith(".xls"))) {
            throw new IllegalArgumentException("模板文件必须是Excel格式 (.xlsx 或 .xls)");
        }

        // 如果有附件文件，验证格式
        if (hasAttachmentFile()) {
            String attachmentFileName = attachmentFileInfo.getFileName();
            if (attachmentFileName == null ||
                (!attachmentFileName.toLowerCase().endsWith(".zip") &&
                 !attachmentFileName.toLowerCase().endsWith(".rar"))) {
                throw new IllegalArgumentException("附件文件必须是压缩格式 (.zip 或 .rar)");
            }
        }
    }
}
