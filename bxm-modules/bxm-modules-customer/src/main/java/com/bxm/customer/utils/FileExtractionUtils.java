package com.bxm.customer.utils;

import com.github.junrar.Archive;
import com.github.junrar.rarfile.FileHeader;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * 文件解压工具类
 * 支持ZIP和RAR格式的文件解压
 *
 * <AUTHOR>
 * @date 2025-08-27
 */
@Slf4j
public class FileExtractionUtils {

    /**
     * 根据文件扩展名自动选择解压方法
     *
     * @param inputStream 压缩文件输入流
     * @param fileName 文件名（用于判断文件类型）
     * @param extractDir 解压目标目录
     * @return 解压后的文件列表
     * @throws Exception 解压失败时抛出异常
     */
    public static List<String> extractArchive(InputStream inputStream, String fileName, String extractDir) throws Exception {
        if (fileName == null || fileName.trim().isEmpty()) {
            throw new IllegalArgumentException("File name cannot be null or empty");
        }

        if (extractDir == null || extractDir.trim().isEmpty()) {
            throw new IllegalArgumentException("Extract directory cannot be null or empty");
        }

        // 创建解压目录
        Path extractPath = Paths.get(extractDir);
        if (!Files.exists(extractPath)) {
            Files.createDirectories(extractPath);
            log.info("Created extract directory: {}", extractDir);
        }

        String lowerFileName = fileName.toLowerCase();

        if (lowerFileName.endsWith(".zip")) {
            return extractZipFile(inputStream, extractDir);
        } else if (lowerFileName.endsWith(".rar")) {
            return extractRarFile(inputStream, extractDir);
        } else {
            throw new IllegalArgumentException("Unsupported file format, only .zip and .rar formats are supported");
        }
    }

    /**
     * 解压ZIP文件
     *
     * @param inputStream ZIP文件输入流
     * @param extractDir 解压目标目录
     * @return 解压后的文件列表
     * @throws Exception 解压失败时抛出异常
     */
    public static List<String> extractZipFile(InputStream inputStream, String extractDir) throws Exception {
        List<String> extractedFiles = new ArrayList<>();

        try (ZipInputStream zipInputStream = new ZipInputStream(inputStream)) {
            ZipEntry entry;

            while ((entry = zipInputStream.getNextEntry()) != null) {
                try {
                    if (!entry.isDirectory()) {
                        // 构造解压文件的完整路径
                        String entryName = entry.getName();
                        String safePath = normalizePath(entryName);
                        Path filePath = Paths.get(extractDir, safePath);

                        // 确保父目录存在
                        Path parentDir = filePath.getParent();
                        if (parentDir != null && !Files.exists(parentDir)) {
                            Files.createDirectories(parentDir);
                        }

                        // 解压文件内容
                        try (OutputStream outputStream = Files.newOutputStream(filePath)) {
                            byte[] buffer = new byte[1024];
                            int len;
                            while ((len = zipInputStream.read(buffer)) > 0) {
                                outputStream.write(buffer, 0, len);
                            }
                        }

                        extractedFiles.add(filePath.toString());
                    }
                } finally {
                    zipInputStream.closeEntry();
                }
            }
        }

        log.info("ZIP file extraction completed, extracted {} files to directory: {}", extractedFiles.size(), extractDir);
        return extractedFiles;
    }

    /**
     * 解压RAR文件
     *
     * @param inputStream RAR文件输入流
     * @param extractDir 解压目标目录
     * @return 解压后的文件列表
     * @throws Exception 解压失败时抛出异常
     */
    public static List<String> extractRarFile(InputStream inputStream, String extractDir) throws Exception {
        List<String> extractedFiles = new ArrayList<>();

        // 使用junrar库解压RAR文件
        try (Archive archive = new Archive(inputStream)) {
            FileHeader fileHeader;

            while ((fileHeader = archive.nextFileHeader()) != null) {
                if (!fileHeader.isDirectory()) {
                    String entryName = fileHeader.getFileNameString();
                    String safePath = normalizePath(entryName);
                    Path filePath = Paths.get(extractDir, safePath);

                    // 确保父目录存在
                    Path parentDir = filePath.getParent();
                    if (parentDir != null && !Files.exists(parentDir)) {
                        Files.createDirectories(parentDir);
                    }

                    // 解压文件内容
                    try (OutputStream outputStream = Files.newOutputStream(filePath)) {
                        archive.extractFile(fileHeader, outputStream);
                    }

                    extractedFiles.add(filePath.toString());
                    log.debug("Extracted file: {} -> {}", entryName, filePath);
                }
            }
        }

        log.info("RAR file extraction completed, extracted {} files to directory: {}", extractedFiles.size(), extractDir);
        return extractedFiles;
    }

    /**
     * 规范化路径，防止路径遍历攻击
     *
     * @param path 原始路径
     * @return 规范化后的安全路径
     */
    private static String normalizePath(String path) {
        if (path == null) {
            return "";
        }

        // 移除路径中的危险字符，防止路径遍历
        String normalizedPath = path.replaceAll("\\.\\.", "").replaceAll("\\\\", "/");

        // 确保路径不以/开头（避免绝对路径）
        if (normalizedPath.startsWith("/")) {
            normalizedPath = normalizedPath.substring(1);
        }

        return normalizedPath;
    }

    /**
     * 清理解压目录
     *
     * @param extractDir 要清理的目录路径
     */
    public static void cleanupExtractDir(String extractDir) {
        if (extractDir == null || extractDir.trim().isEmpty()) {
            return;
        }

        try {
            Path dirPath = Paths.get(extractDir);
            if (Files.exists(dirPath)) {
                // 递归删除目录及其内容
                Files.walk(dirPath)
                    .sorted((a, b) -> b.compareTo(a)) // 先删除文件，再删除目录
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            log.warn("Failed to delete file: {}, error: {}", path, e.getMessage());
                        }
                    });
                log.info("Extract directory cleanup completed: {}", extractDir);
            }
        } catch (Exception e) {
            log.error("Failed to cleanup extract directory: {}, error: {}", extractDir, e.getMessage(), e);
        }
    }


}
